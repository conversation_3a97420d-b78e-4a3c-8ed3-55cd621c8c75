package com.pokecobble.town;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.data.PlayerDataStorage;
import com.pokecobble.town.data.PlayerDataStorage.SerializablePlayerData;
import com.pokecobble.town.data.PlayerDataUtils;
import com.pokecobble.town.network.PacketValidator;
import com.pokecobble.town.network.player.PlayerDataSynchronizer;
import com.pokecobble.town.network.security.PacketAuthenticator;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Manages player data, including loading, saving, and synchronization.
 */
public class PlayerDataManager {
    private static PlayerDataManager instance;

    // Map of player UUIDs to their TownPlayer objects (thread-safe)
    private final Map<UUID, TownPlayer> players = new ConcurrentHashMap<>();

    // Reference to the server
    private MinecraftServer server;

    // Lock for player data operations
    private final ReadWriteLock playerLock = new ReentrantReadWriteLock();

    // Flag to track initialization
    private boolean initialized = false;

    // Map to track when players joined (for play time calculation)
    private final Map<UUID, Long> playerJoinTimes = new ConcurrentHashMap<>();

    /**
     * Private constructor for singleton pattern.
     */
    private PlayerDataManager() {
        // Private constructor
    }

    /**
     * Gets the singleton instance of the PlayerDataManager.
     *
     * @return The PlayerDataManager instance
     */
    public static PlayerDataManager getInstance() {
        if (instance == null) {
            instance = new PlayerDataManager();
        }
        return instance;
    }

    /**
     * Sets the server reference and initializes the player data system.
     *
     * @param server The server
     */
    public void setServer(MinecraftServer server) {
        this.server = server;

        if (server != null && !initialized) {
            // Initialize player data storage
            PlayerDataStorage.initialize();

            // Clear any existing data
            players.clear();
            playerJoinTimes.clear();

            initialized = true;
            Pokecobbleclaim.LOGGER.info("PlayerDataManager initialized");
        } else if (server == null) {
            initialized = false;
        }
    }

    /**
     * Gets a player by their UUID.
     *
     * @param playerId The UUID of the player
     * @return The TownPlayer object, or null if not found
     */
    public TownPlayer getPlayer(UUID playerId) {
        // Acquire read lock to ensure thread safety
        playerLock.readLock().lock();
        try {
            return players.get(playerId);
        } finally {
            playerLock.readLock().unlock();
        }
    }

    /**
     * Adds or updates a player.
     *
     * @param player The TownPlayer object
     */
    public void addPlayer(TownPlayer player) {
        // Acquire write lock to ensure thread safety
        playerLock.writeLock().lock();
        try {
            players.put(player.getUuid(), player);
        } finally {
            playerLock.writeLock().unlock();
        }
    }

    /**
     * Removes a player.
     *
     * @param playerId The UUID of the player
     */
    public void removePlayer(UUID playerId) {
        // Acquire write lock to ensure thread safety
        playerLock.writeLock().lock();
        try {
            players.remove(playerId);
        } finally {
            playerLock.writeLock().unlock();
        }
    }

    /**
     * Handles a player joining the server.
     *
     * @param player The player entity
     */
    public void onPlayerJoin(ServerPlayerEntity player) {
        // Validate player
        try {
            PacketValidator.validatePlayer(player);
        } catch (IllegalArgumentException e) {
            Pokecobbleclaim.LOGGER.error("Invalid player joined: " + e.getMessage());
            return;
        }

        UUID playerId = player.getUuid();
        String playerName = player.getName().getString();

        // Generate security token for this player
        byte[] token = PacketAuthenticator.generateToken(playerId);

        // Record join time for play time tracking
        playerJoinTimes.put(playerId, System.currentTimeMillis());

        // Acquire write lock
        playerLock.writeLock().lock();

        try {
            // Load player data from disk
            SerializablePlayerData playerData = PlayerDataStorage.loadPlayerData(playerId);

            // Check if player exists in a town
            UUID townId = TownManager.getInstance().getPlayerTownId(playerId);
            Town town = townId != null ? TownManager.getInstance().getTownById(townId) : null;

            // Debug logging to track the player join process
            Pokecobbleclaim.LOGGER.debug("Player " + playerName + " joining - PlayerData exists: " + (playerData != null) +
                                       ", TownId: " + townId + ", Town exists: " + (town != null));

            if (playerData != null) {
                // Player data exists, update with current information
                TownPlayer townPlayer = playerData.toTownPlayer();
                townPlayer.setName(playerName); // Update name in case it changed
                townPlayer.setOnline(true);

                // Add player to manager
                addPlayer(townPlayer);

                // Update player data in town if player is in a town
                if (town != null) {
                    town.addPlayer(townPlayer);

                    // Broadcast player list update to all clients when a player comes online
                    // This ensures all players see the updated online status immediately
                    TownManager.getInstance().broadcastTownPlayerListUpdate(town);
                }

                // Update last login time
                playerData.updateLastLogin();

                // Save updated player data
                PlayerDataStorage.savePlayerData(playerId, townPlayer, townId);

                Pokecobbleclaim.LOGGER.info("Updated player data for " + playerName + " in town " + (town != null ? town.getName() : "none"));

                Pokecobbleclaim.LOGGER.info("Loaded existing player data for " + playerName);
            } else if (town != null) {
                // Player is in a town but no separate player data exists yet
                // Get player data from town
                TownPlayer townPlayer = town.getPlayer(playerId);

                Pokecobbleclaim.LOGGER.debug("Player " + playerName + " in town " + town.getName() +
                                           " - TownPlayer exists: " + (townPlayer != null));

                if (townPlayer == null) {
                    // Player is in town mapping but not in town player list
                    // This can happen if TownPlayer objects weren't created during town loading
                    // Get the actual rank from the town's authoritative rank storage
                    TownPlayerRank rank = town.getPlayerRank(playerId);
                    if (rank == null) {
                        // This should not happen with the new system, but handle gracefully
                        rank = TownPlayerRank.MEMBER; // Default to MEMBER, never VISITOR for town members
                        Pokecobbleclaim.LOGGER.warn("No rank data found for player " + playerName + " in town " + town.getName() + ", defaulting to MEMBER");
                    } else {
                        Pokecobbleclaim.LOGGER.info("Restored rank " + rank + " (" + rank.getDisplayName() + ") for player " + playerName + " in town " + town.getName());
                    }

                    // Validate rank - ensure town members are never VISITOR
                    if (rank == TownPlayerRank.VISITOR) {
                        Pokecobbleclaim.LOGGER.warn("Player " + playerName + " had VISITOR rank in town " + town.getName() + ", correcting to MEMBER");
                        rank = TownPlayerRank.MEMBER;
                        town.setPlayerRank(playerId, rank); // Update the authoritative storage
                    }

                    townPlayer = new TownPlayer(playerId, playerName, rank);
                    town.addPlayer(townPlayer);
                }

                // Update player name and online status
                townPlayer.setName(playerName);
                townPlayer.setOnline(true);

                // Add player to manager
                addPlayer(townPlayer);

                // Create and save new player data
                PlayerDataStorage.savePlayerData(playerId, townPlayer, townId);

                // Broadcast player list update to all clients when a player comes online
                // This ensures all players see the updated online status immediately
                TownManager.getInstance().broadcastTownPlayerListUpdate(town);

                Pokecobbleclaim.LOGGER.info("Created new player data for " + playerName + " from town data");
            } else {
                // New player, not in a town
                // Create new player data - use MEMBER as default for consistency (VISITOR is for display only)
                TownPlayer townPlayer = new TownPlayer(playerId, playerName, TownPlayerRank.MEMBER);
                townPlayer.setOnline(true);

                // Add player to manager
                addPlayer(townPlayer);

                // Save new player data
                PlayerDataStorage.savePlayerData(playerId, townPlayer, null);

                Pokecobbleclaim.LOGGER.info("Created new player data for " + playerName);
            }

            // Synchronize player data to client
            if (server != null) {
                PlayerDataSynchronizer.syncPlayerData(server, playerId);

                // If player is in a town, also send town membership data to client
                if (townId != null) {
                    com.pokecobble.town.network.town.TownDataSynchronizer.syncPlayerTownData(server, playerId);
                    Pokecobbleclaim.LOGGER.info("Synchronized town membership for " + playerName + " to town " + townId);
                }
            }
        } finally {
            // Release write lock
            playerLock.writeLock().unlock();
        }
    }

    /**
     * Handles a player leaving the server.
     *
     * @param player The player entity
     */
    public void onPlayerLeave(ServerPlayerEntity player) {
        // Validate player
        try {
            PacketValidator.validatePlayer(player);
        } catch (IllegalArgumentException e) {
            Pokecobbleclaim.LOGGER.error("Invalid player left: " + e.getMessage());
            return;
        }

        UUID playerId = player.getUuid();

        // Remove security token for this player
        PacketAuthenticator.removeToken(playerId);

        // Calculate play time
        long joinTime = playerJoinTimes.getOrDefault(playerId, System.currentTimeMillis());
        long playTime = System.currentTimeMillis() - joinTime;

        // Remove join time tracking
        playerJoinTimes.remove(playerId);

        // Acquire write lock
        playerLock.writeLock().lock();

        try {
            // Get player data
            TownPlayer townPlayer = getPlayer(playerId);
            if (townPlayer != null) {
                // Update online status
                townPlayer.setOnline(false);

                // Get town ID
                UUID townId = TownManager.getInstance().getPlayerTownId(playerId);

                // Update player data in town if player is in a town
                if (townId != null) {
                    Town town = TownManager.getInstance().getTownById(townId);
                    if (town != null) {
                        town.addPlayer(townPlayer);

                        // Broadcast player list update to all clients when a player goes offline
                        // This ensures all players see the updated offline status immediately
                        TownManager.getInstance().broadcastTownPlayerListUpdate(town);
                    }
                }

                // Load existing player data to update play time
                SerializablePlayerData playerData = PlayerDataStorage.loadPlayerData(playerId);
                if (playerData != null) {
                    // Update total play time
                    long totalPlayTime = playerData.getTotalPlayTime() + playTime;
                    playerData.setTotalPlayTime(totalPlayTime);

                    // Save updated player data
                    PlayerDataStorage.savePlayerData(playerId, townPlayer, townId);
                }

                // Remove from cache to save memory
                PlayerDataStorage.removeFromCache(playerId);
            }
        } finally {
            // Release write lock
            playerLock.writeLock().unlock();
        }
    }

    /**
     * Saves all player data to disk.
     */
    public void saveAllPlayers() {
        // Acquire read lock to ensure thread safety while iterating
        playerLock.readLock().lock();
        try {
            // Create a copy of the player entries to avoid concurrent modification
            Map<UUID, TownPlayer> playersCopy = new HashMap<>(players);

            // Release the lock before saving to avoid holding it during I/O operations
            playerLock.readLock().unlock();

            // Save each player's data
            for (Map.Entry<UUID, TownPlayer> entry : playersCopy.entrySet()) {
                UUID playerId = entry.getKey();
                TownPlayer townPlayer = entry.getValue();

                // Get town ID
                UUID townId = TownManager.getInstance().getPlayerTownId(playerId);

                // Save player data
                PlayerDataStorage.savePlayerData(playerId, townPlayer, townId);
            }

            Pokecobbleclaim.LOGGER.info("Saved all player data");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save all player data: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Ensure the lock is released if an exception occurred before unlocking
            if (playerLock instanceof ReentrantReadWriteLock) {
                ReentrantReadWriteLock reentrantLock = (ReentrantReadWriteLock) playerLock;
                if (reentrantLock.getReadHoldCount() > 0) {
                    playerLock.readLock().unlock();
                }
            }
        }
    }

    /**
     * Updates a player's data and synchronizes it.
     *
     * @param playerId The UUID of the player
     * @param townPlayer The updated TownPlayer object
     */
    public void updatePlayer(UUID playerId, TownPlayer townPlayer) {
        // Validate inputs
        try {
            PacketValidator.validateUUID(playerId);
        } catch (IllegalArgumentException e) {
            Pokecobbleclaim.LOGGER.error("Invalid player ID for updatePlayer: " + e.getMessage());
            return;
        }

        if (townPlayer == null) {
            Pokecobbleclaim.LOGGER.error("Town player is null in updatePlayer");
            return;
        }

        // Increment data version to trigger synchronization
        townPlayer.incrementDataVersion();

        // Update player in manager (this method already handles locking)
        addPlayer(townPlayer);

        // Get town ID
        UUID townId = TownManager.getInstance().getPlayerTownId(playerId);

        // Save player data
        PlayerDataStorage.savePlayerData(playerId, townPlayer, townId);

        // Synchronize player data
        if (server != null) {
            PlayerDataSynchronizer.syncPlayerData(server, playerId);
        }
    }
}
